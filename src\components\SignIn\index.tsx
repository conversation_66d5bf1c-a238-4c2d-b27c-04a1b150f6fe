"use client"

import { FacebookButton } from "src/components/Buttons/FacebookButton"
import { GoogleButton } from "src/components/Buttons/GoogleButton"
import { Title, Stack, Container, Box, Text, Alert, Group } from "@mantine/core"
import { IconCoins, IconGift } from "@tabler/icons-react"

import styles from "./index.module.scss"

import { useSession } from "next-auth/react";

export const SignIn = () => {

  return (
    <Container
      className={styles.signin_container}
      p="3rem"
      h="10vh"
      w="100%"
      display="flex"
      style={{
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <Box ta="center" w="100%">
        <Title
          order={1}
          ta="center"
          mb="md"
        >
          Sign in with your account
        </Title>

        {/* Signup Incentive */}
        <Alert
          icon={<IconGift size={20} />}
          title="🎉 Welcome Bonus!"
          color="green"
          variant="light"
          mb="lg"
        >
          <Group gap="xs" align="center" justify="center">
            <IconCoins size={16} />
            <Text size="sm" fw={500}>
              Get 2000 points instantly when you sign up!
            </Text>
          </Group>
          <Text size="xs" c="dimmed" ta="center" mt="xs">
            Use points to create ODude Names and unlock features
          </Text>
        </Alert>

        <Stack className={styles.btn_group} align="center">
          <GoogleButton />
        </Stack>
      </Box>
    </Container>
  )
}
